{"semi": true, "trailingComma": "all", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "tailwindConfig": "./tailwind.config.ts", "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrder": ["^(react/(.*)$)|^(react$)", "^(next/(.*)$)|^(next$)", "<THIRD_PARTY_MODULES>", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}