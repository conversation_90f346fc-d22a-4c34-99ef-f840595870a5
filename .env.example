NEXT_PUBLIC_PRIVY_APP_ID=
NEXT_PUBLIC_EAP_RECEIVE_WALLET_ADDRESS=
NEXT_PUBLIC_HELIUS_RPC_URL=
NEXT_PUBLIC_SUB_LAMPORTS=1000000
NEXT_PUBLIC_SUB_ENABLED=false
NEXT_PUBLIC_TRIAL_LAMPORTS=0
NEXT_PUBLIC_TRIAL_ENABLED=false
NEXT_PUBLIC_NEUR_MINT=********************************************
NEXT_PUBLIC_DISABLED_TOOLS=[] #["getTokenOrders", "holders", "resolveWalletAddressFromDomain"]
OPENAI_API_KEY=
PRIVY_APP_SECRET=
WALLET_ENCRYPTION_KEY=
NEXT_PUBLIC_IMGBB_API_KEY=
ANTHROPIC_API_KEY=
DATABASE_URL=
DIRECT_URL=
CRON_SECRET=
HELIUS_API_KEY=
# coingecko api key and base url
CG_API_KEY=<optional>
CG_BASE_URL=
TELEGRAM_BOT_TOKEN=
TELEGRAM_BOT_USERNAME=<optional>
DISCORD_BOT_TOKEN=<optional>
DISCORD_GUILD_ID=<optional>
DISCORD_ROLE_ID=<optional>
PRIVY_SIGNING_KEY=<optional>
BIRDEYE_API_KEY=<optional>
COOKIE_FUN_API_KEY=<optional>
