generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model SavedPrompt {
  id              String   @id @default(cuid())
  userId          String    
  title           String   @db.VarChar(255)
  content         String   @db.Text
  usageFrequency  Int      @default(0)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  lastUsedAt      DateTime?
  isFavorite      Boolean  @default(false)

  user            User     @relation(fields: [userId], references: [id])

  @@index([userId, lastUsedAt], name: "idx_userId_lastUsedAt") 
  @@map("saved_prompts") 
}

model User {
  id        String     @id @default(cuid())
  privyId   String     @unique
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  earlyAccess Boolean  @default(false)
  degenMode  Boolean   @default(false)
  referralCode String? @unique
  referringUserId String?

  wallets       Wallet[]
  conversations Conversation[]
  Rule          Rule[]
  Action        Action[]
  tokenStats    TokenStat[]
  TelegramChat  TelegramChat[]
  SavedPrompts SavedPrompt[]
  subscription  Subscription?

  @@map("users")
}

model Wallet {
  id                  String       @id @default(cuid())
  ownerId             String
  name                String
  publicKey           String
  encryptedPrivateKey String?      @db.Text
  walletSource        WalletSource @default(CUSTOM)
  chain               Chain        @default(SOLANA)
  delegated           Boolean      @default(false)
  active              Boolean      @default(true)
  createdAt           DateTime     @default(now())
  updatedAt           DateTime     @updatedAt

  owner User @relation(fields: [ownerId], references: [id])

  @@unique([ownerId, publicKey])
  @@map("wallets")
}

model Conversation {
  id         String     @id @default(uuid())
  userId     String
  title      String
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  lastReadAt DateTime?  @default(now())
  lastMessageAt DateTime?
  visibility Visibility @default(PRIVATE)

  user     User      @relation(fields: [userId], references: [id])
  messages Message[]
  Action   Action[]

  @@map("conversations")
}

model Message {
  id             String   @id @default(uuid())
  conversationId String
  role           String
  content        String?
  toolInvocations Json?
  experimental_attachments Json?
  createdAt      DateTime @default(now())

  conversation Conversation @relation(fields: [conversationId], references: [id])

  @@map("messages")
}

enum Operator {
  eq
  lt
  gt
  contains
}

model Rule {
  id        String   @id @default(cuid())
  userId    String
  name      String   @db.VarChar(255)
  field     String   @db.VarChar(255) // Field to evaluate (e.g., "transaction.amount", "transaction.sender")
  operator  Operator
  value     String   @db.VarChar(255) // Value to compare against (e.g., "100", "0x1234")
  triggered Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("rules")
}

model Action {
  id             String    @id @default(cuid())
  userId         String
  conversationId String
  triggeredBy    Int[] // Array of rule IDs required to trigger this action
  stoppedBy      Int[] // Array of rule IDs required to stop this action
  frequency      Int? // Frequency in seconds (e.g., 3600 for 1 hour, 86400 for 1 day)
  maxExecutions  Int? // Times to execute before stopping
  name           String?   @db.VarChar(255) // User defined name for the action
  description    String    @db.VarChar(255) // Human readable description of the action, or message to send to AI
  actionType     String    @db.VarChar(255) // Type of action (e.g., "call_function", "invoke_api")
  params         Json? // JSON object for action parameters (e.g., inputs for the function)
  timesExecuted  Int       @default(0)
  lastExecutedAt DateTime?
  lastFailureAt  DateTime?
  lastSuccessAt  DateTime?
  triggered      Boolean   @default(false) // Whether the action has been triggered. True if triggeredBy is empty
  paused         Boolean   @default(false)
  completed      Boolean   @default(false)
  priority       Int       @default(0) // Priority level for execution, higher numbers execute first
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  startTime      DateTime? // Time to start executing the action

  user         User         @relation(fields: [userId], references: [id])
  conversation Conversation @relation(fields: [conversationId], references: [id])

  @@index([triggeredBy], name: "triggeredBy_idx")
  @@index([stoppedBy], name: "stoppedBy_idx")
  @@map("actions")
}

model TokenStat {
  id               String   @id @default(cuid())
  userId           String
  messageIds       String[]
  promptTokens     Int
  completionTokens Int
  totalTokens      Int
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user User @relation(fields: [userId], references: [id])

  @@map("token_stats")
}

enum Visibility {
  PRIVATE
  PUBLIC
}

enum WalletSource {
  CUSTOM
  PRIVY
}

enum Chain {
  SOLANA
}

model TelegramChat {
  id        String   @id @default(cuid())
  userId    String   @unique
  username  String
  chatId    String?
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])

  @@map("telegram_chats")
}

model Subscription {
  id             String           @id @default(cuid())
  userId         String           @unique
  createdAt      DateTime         @default(now())
  active         Boolean          @default(true)
  startDate      DateTime         @default(now())  // When the subscription starts
  nextPaymentDate DateTime                        // The date for the next payment
  endDate        DateTime?                        // When the subscription ends (nullable)
  billingCycle   BillingCycle     @default(MONTHLY) // Enum for billing frequency

  user           User             @relation(fields: [userId], references: [id])
  payments       SubscriptionPayment[] // One-to-many relation to payments

  @@map("subscriptions")
}

model SubscriptionPayment {
  id              String         @id @default(cuid())
  subscriptionId  String
  paymentDate     DateTime       // When the payment was processed
  amount          Decimal        // Payment amount, changed from Float to Decimal
  status          PaymentStatus  // Status of the payment (e.g., success, failed, pending)
  failureReason   String?        // Reason for payment failure
  failureCode     Int?           // Payment failure code
  createdAt       DateTime       @default(now())
  transactionHash String?        // Transaction hash for the payment

  subscription    Subscription   @relation(fields: [subscriptionId], references: [id])

  @@map("subscription_payments")
}

enum BillingCycle {
  MONTHLY
  YEARLY
}

enum PaymentStatus {
  SUCCESS
  FAILED
  PENDING
}
