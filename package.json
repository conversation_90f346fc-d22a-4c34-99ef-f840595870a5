{"name": "neur-app", "version": "0.3.4", "private": true, "scripts": {"vercel-build": "NODE_OPTIONS=--max_old_space_size=2048 pnpm npx prisma generate && next build", "dev": "NODE_NO_WARNINGS=1 next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:reset": "pnpm npx prisma generate && pnpm npx prisma migrate reset && pnpm npx prisma db push", "generate": "prisma generate", "migrate": "prisma migrate deploy", "dev:up-build": "docker compose up --build -d", "dev:up": "docker compose up -d", "format": "prettier --write \"**/*.{ts,tsx,md,json,js,jsx}\"", "format:check": "prettier --check \"**/*.{ts,tsx,md,json,js,jsx}\""}, "dependencies": {"@ai-sdk/anthropic": "^1.1.0", "@ai-sdk/openai": "^1.1.0", "@bonfida/spl-name-service": "^3.0.7", "@mem0/vercel-ai-provider": "^0.0.10", "@prisma/client": "^6.2.1", "@privy-io/react-auth": "^1.99.1", "@privy-io/server-auth": "^1.18.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-form": "^0.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.6", "@remixicon/react": "^4.6.0", "@solana/spl-token": "^0.4.9", "@solana/web3.js": "1.98.0", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.1.0", "ai": "^4.1.0", "bs58": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^11.18.1", "helius-sdk": "^1.4.1", "keyv": "4.5.4", "lodash": "^4.17.21", "lucide-react": "^0.468.0", "mem0ai": "^1.0.31", "moment": "^2.30.1", "nanoid": "^5.0.9", "next": "15.1.2", "next-safe-action": "^7.10.2", "next-themes": "^0.4.4", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-markdown": "^9.0.3", "react-particles": "^2.12.2", "react-tweet": "^3.2.1", "recharts": "^2.15.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "solana-agent-kit": "github:slimeonmyhead/solana-agent-kit#c571e8b59ed7d399a705578af2d58c95f7e9e826", "sonner": "^1.7.2", "swr": "^2.3.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tsparticles-slim": "^2.12.0", "uuid": "^11.0.5", "yet-another-react-lightbox": "^3.21.7", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/typography": "^0.5.16", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/lodash": "^4.17.14", "@types/node": "^20.17.14", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "eslint-config-next": "15.1.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.5.14", "prisma": "^6.2.1", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}, "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}